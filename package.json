{"name": "rss-browser", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.5.21"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.8.1", "typescript": "~5.8.3", "utools-api-types": "^7.2.0", "vite": "npm:rolldown-vite@7.1.12", "vue-tsc": "^3.0.7"}, "pnpm": {"overrides": {"vite": "npm:rolldown-vite@7.1.12"}}}
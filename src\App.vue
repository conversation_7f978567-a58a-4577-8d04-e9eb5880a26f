<script setup lang="ts">
import { ref, onMounted } from "vue";

// 定义响应式数据
const currentFeature = ref("");
const inputText = ref("");
const utoolsInfo = ref<any>({});
const testResults = ref<string[]>([]);

// 获取当前功能代码
onMounted(() => {
  // 检查是否在 utools 环境中
  if (typeof window !== "undefined" && (window as any).utools) {
    const utools = (window as any).utools;

    // 获取当前功能信息
    currentFeature.value = utools.getCurrentFeatureCode() || "unknown";

    // 获取 utools 基本信息
    utoolsInfo.value = {
      version: utools.getVersion(),
      user: utools.getUser(),
      currentFeature: currentFeature.value,
    };

    // 监听输入变化
    utools.onPluginEnter(({ code, type, payload }: any) => {
      currentFeature.value = code;
      if (payload) {
        inputText.value = payload;
      }
      addTestResult(
        `插件启动: ${code}, 类型: ${type}, 数据: ${payload || "无"}`
      );
    });
  } else {
    addTestResult("当前不在 utools 环境中，使用模拟数据");
    currentFeature.value = "rss-main";
  }
});

// 添加测试结果
const addTestResult = (message: string) => {
  testResults.value.unshift(`[${new Date().toLocaleTimeString()}] ${message}`);
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10);
  }
};

// 测试功能函数
const testUtoolsAPI = () => {
  if (typeof window !== "undefined" && (window as any).utools) {
    const utools = (window as any).utools;
    addTestResult("utools API 可用");
    addTestResult(`版本: ${utools.getVersion()}`);
  } else {
    addTestResult("utools API 不可用");
  }
};

const testServices = () => {
  if (typeof window !== "undefined" && (window as any).services) {
    addTestResult("services 预加载脚本可用");
    try {
      // 测试写入文件功能
      const filePath = (window as any).services.writeTextFile(
        "RSS浏览器测试文件"
      );
      addTestResult(`文件写入成功: ${filePath}`);
    } catch (error) {
      addTestResult(`文件写入失败: ${error}`);
    }
  } else {
    addTestResult("services 预加载脚本不可用");
  }
};

const simulateRSSAdd = () => {
  const testUrl = "https://example.com/feed.xml";
  inputText.value = testUrl;
  addTestResult(`模拟添加RSS: ${testUrl}`);
};

const clearResults = () => {
  testResults.value = [];
};
</script>

<template>
  <div class="app-container">
    <header class="header">
      <h1>🔖 RSS浏览器测试界面</h1>
      <p class="subtitle">当前功能: {{ currentFeature }}</p>
    </header>

    <main class="main-content">
      <!-- 功能测试区域 -->
      <section class="test-section">
        <h2>功能测试</h2>
        <div class="button-group">
          <button @click="testUtoolsAPI" class="test-btn">
            测试 utools API
          </button>
          <button @click="testServices" class="test-btn">测试预加载脚本</button>
          <button @click="simulateRSSAdd" class="test-btn">模拟添加RSS</button>
          <button @click="clearResults" class="test-btn clear-btn">
            清空结果
          </button>
        </div>
      </section>

      <!-- 输入测试区域 -->
      <section class="input-section">
        <h2>输入测试</h2>
        <input
          v-model="inputText"
          placeholder="输入RSS链接或搜索内容..."
          class="test-input"
        />
        <p class="input-display">当前输入: {{ inputText || "无" }}</p>
      </section>

      <!-- 系统信息区域 -->
      <section class="info-section">
        <h2>系统信息</h2>
        <div class="info-grid">
          <div class="info-item">
            <strong>utools版本:</strong> {{ utoolsInfo.version || "未知" }}
          </div>
          <div class="info-item">
            <strong>当前功能:</strong> {{ utoolsInfo.currentFeature || "未知" }}
          </div>
          <div class="info-item">
            <strong>用户信息:</strong>
            {{ utoolsInfo.user?.nickname || "未登录" }}
          </div>
        </div>
      </section>

      <!-- 测试结果区域 -->
      <section class="results-section">
        <h2>测试结果</h2>
        <div class="results-container">
          <div
            v-for="(result, index) in testResults"
            :key="index"
            class="result-item"
          >
            {{ result }}
          </div>
          <div v-if="testResults.length === 0" class="no-results">
            暂无测试结果
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<style scoped>
.app-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header h1 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 28px;
}

.subtitle {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

.main-content {
  display: grid;
  gap: 20px;
}

section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

section h2 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 20px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: #3498db;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.test-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.clear-btn {
  background: #e74c3c;
}

.clear-btn:hover {
  background: #c0392b;
}

.test-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 10px;
}

.test-input:focus {
  outline: none;
  border-color: #3498db;
}

.input-display {
  margin: 0;
  color: #7f8c8d;
  font-size: 14px;
}

.info-grid {
  display: grid;
  gap: 10px;
}

.info-item {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
}

.results-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 10px;
  background: #f8f9fa;
}

.result-item {
  padding: 8px;
  margin-bottom: 5px;
  background: white;
  border-radius: 4px;
  font-size: 13px;
  font-family: "Courier New", monospace;
  border-left: 3px solid #3498db;
}

.no-results {
  text-align: center;
  color: #7f8c8d;
  font-style: italic;
  padding: 20px;
}

@media (max-width: 600px) {
  .app-container {
    padding: 10px;
  }

  .button-group {
    flex-direction: column;
  }

  .test-btn {
    width: 100%;
  }
}
</style>

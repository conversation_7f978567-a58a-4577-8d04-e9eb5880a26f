# utools 插件 plugin.json 配置指南

## 基本配置字段

### 必需字段

- **name**: 插件名称，显示在 utools 插件列表中
- **version**: 插件版本号，建议使用语义化版本 (如 "1.0.0")
- **main**: 插件主入口文件路径 (相对于 plugin.json)
- **features**: 功能配置数组

### 可选字段

- **description**: 插件描述
- **author**: 插件作者
- **homepage**: 插件主页或仓库地址
- **preload**: 预加载脚本路径，用于与 utools API 交互
- **logo**: 插件图标文件路径
- **platform**: 支持的平台 `["win32", "darwin", "linux"]`

## 插件设置 (pluginSetting)

```json
"pluginSetting": {
  "single": true,        // 是否单例模式
  "height": 600,         // 插件窗口高度
  "width": 800,          // 插件窗口宽度
  "resizable": false,    // 是否可调整大小
  "alwaysOnTop": false   // 是否置顶
}
```

## 功能配置 (features)

每个功能包含：
- **code**: 功能唯一标识符
- **explain**: 功能说明
- **cmds**: 触发命令配置

### 命令类型

#### 1. 关键词命令
```json
"cmds": ["关键词1", "关键词2"]
```

#### 2. 正则表达式命令
```json
"cmds": [
  {
    "type": "regex",
    "label": "命令标签",
    "match": "正则表达式",
    "minLength": 最小长度,
    "maxLength": 最大长度
  }
]
```

#### 3. 窗口命令 (window)
```json
"cmds": [
  {
    "type": "window",
    "match": {
      "app": ["应用名称"],
      "title": "窗口标题正则",
      "class": "窗口类名"
    }
  }
]
```

#### 4. 文件命令 (files)
```json
"cmds": [
  {
    "type": "files",
    "label": "处理文件",
    "fileType": "文件类型",
    "match": ["*.txt", "*.md"],
    "minNum": 1,
    "maxNum": 10
  }
]
```

#### 5. 超级面板命令 (over)
```json
"cmds": [
  {
    "type": "over",
    "label": "超级面板",
    "exclude": "排除的正则表达式"
  }
]
```

## 开发配置

```json
"development": {
  "main": "http://localhost:5173",  // 开发服务器地址
  "preload": "preload/dev.js"      // 开发环境预加载脚本
}
```

## 完整示例

```json
{
  "name": "RSS浏览器",
  "version": "1.0.0",
  "description": "一个用于浏览和管理RSS订阅的utools插件",
  "author": "你的名字",
  "homepage": "https://github.com/your-username/rss-browser",
  "main": "index.html",
  "preload": "preload/services.js",
  "logo": "logo.png",
  "platform": ["win32", "darwin", "linux"],
  "pluginSetting": {
    "single": true,
    "height": 600
  },
  "development": {
    "main": "http://localhost:5173"
  },
  "features": [
    {
      "code": "rss-main",
      "explain": "RSS浏览器主界面",
      "cmds": ["rss", "RSS", "RSS浏览器", "订阅", "新闻"]
    },
    {
      "code": "rss-add",
      "explain": "添加RSS订阅",
      "cmds": [
        {
          "type": "regex",
          "label": "添加RSS订阅",
          "match": "^(https?://.+\\.(xml|rss|atom))$",
          "minLength": 10
        }
      ]
    }
  ]
}
```

## 注意事项

1. **文件路径**: 所有路径都相对于 plugin.json 文件
2. **编码**: 文件必须使用 UTF-8 编码
3. **版本号**: 建议使用语义化版本规范
4. **图标**: 建议使用 PNG 格式，尺寸 64x64 像素
5. **预加载脚本**: 用于与 utools API 交互，是插件与系统通信的桥梁

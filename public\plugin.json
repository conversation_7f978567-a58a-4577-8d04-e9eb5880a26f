{"name": "RSS浏览器", "version": "1.0.0", "description": "一个用于浏览和管理RSS订阅的utools插件", "author": "你的名字", "homepage": "https://github.com/your-username/rss-browser", "main": "index.html", "preload": "preload/services.js", "logo": "logo.png", "platform": ["win32", "darwin", "linux"], "pluginSetting": {"single": true, "height": 600}, "development": {"main": "http://localhost:5173"}, "features": [{"code": "rss-main", "explain": "RSS浏览器主界面", "cmds": ["rss", "RSS", "RSS浏览器", "订阅", "新闻"]}, {"code": "rss-add", "explain": "添加RSS订阅", "cmds": [{"type": "regex", "label": "添加RSS订阅", "match": "^(https?://.+\\.(xml|rss|atom))$", "minLength": 10}]}, {"code": "rss-search", "explain": "搜索RSS内容", "cmds": [{"type": "over", "label": "搜索RSS内容", "exclude": "/^\\s*$/"}]}]}
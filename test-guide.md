# utools RSS浏览器插件测试指南

## 测试界面功能说明

这个测试界面是为了帮助你验证 utools 插件的基本功能和配置是否正确。

### 界面功能区域

#### 1. 功能测试区域
- **测试 utools API**: 检查是否能正常访问 utools 的 API
- **测试预加载脚本**: 验证 preload 脚本是否正常加载
- **模拟添加RSS**: 模拟添加一个 RSS 链接
- **清空结果**: 清空测试结果显示

#### 2. 输入测试区域
- 可以输入 RSS 链接或搜索内容
- 实时显示当前输入的内容
- 用于测试不同类型的输入触发

#### 3. 系统信息区域
显示当前的系统信息：
- utools 版本
- 当前功能代码
- 用户登录信息

#### 4. 测试结果区域
- 显示最近 10 条测试结果
- 包含时间戳的日志信息
- 可滚动查看历史记录

## 如何测试

### 1. 开发环境测试
```bash
# 启动开发服务器
npm run dev
# 或
pnpm dev
```

在浏览器中访问 `http://localhost:5173` 查看界面效果。

### 2. utools 环境测试

1. **构建插件**:
   ```bash
   npm run build
   # 或
   pnpm build
   ```

2. **安装到 utools**:
   - 打开 utools
   - 进入插件管理
   - 选择"开发者"选项
   - 点击"安装本地插件"
   - 选择项目根目录

3. **测试功能**:
   - 在 utools 中输入 `rss` 触发主功能
   - 输入 RSS 链接测试正则匹配功能
   - 使用超级面板测试搜索功能

### 3. 功能验证清单

- [ ] 插件能正常启动
- [ ] 界面显示正常
- [ ] utools API 可以访问
- [ ] 预加载脚本正常工作
- [ ] 不同功能代码能正确识别
- [ ] 输入内容能正确接收
- [ ] 文件操作功能正常

## 常见问题

### Q: 界面显示"utools API 不可用"
A: 这是正常的，在浏览器环境中无法访问 utools API。只有在 utools 中运行时才能正常访问。

### Q: 预加载脚本测试失败
A: 检查 `public/preload/services.js` 文件是否存在，以及 plugin.json 中的 preload 路径是否正确。

### Q: 插件无法在 utools 中启动
A: 检查：
1. plugin.json 配置是否正确
2. 构建是否成功
3. 文件路径是否正确
4. 是否有语法错误

## 下一步开发建议

1. **添加 RSS 解析功能**: 集成 RSS 解析库
2. **数据存储**: 使用 utools 的数据存储 API
3. **界面优化**: 根据实际需求优化界面布局
4. **错误处理**: 添加更完善的错误处理机制
5. **功能扩展**: 添加更多 RSS 管理功能

## 相关文档

- [utools 插件开发文档](https://u.tools/docs/developer/welcome.html)
- [Vue 3 官方文档](https://vuejs.org/)
- [Vite 构建工具文档](https://vitejs.dev/)
